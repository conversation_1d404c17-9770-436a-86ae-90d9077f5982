import { useEffect } from 'react';
import { pusherClient } from 'listener/pusher';
import { useUpdateNotifications } from 'hooks/useNotifications';
import { useProfile } from 'hooks/useProfile';
import { NotificationDto } from 'types/notification';

export const NotificationListener = () => {
  const { user } = useProfile();
  const { addNotification } = useUpdateNotifications();

  useEffect(() => {
    // Only listen for notifications if user is admin
    if (!user || user.role !== 'admin') return;

    // Subscribe to admin notifications channel
    const adminChannel = pusherClient.subscribe('admin-notifications');
    
    // Listen for new message notifications
    adminChannel.bind('new-message-notification', (notification: NotificationDto) => {
      addNotification(notification);
      
      // Show browser notification if permission is granted
      if (Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico',
          tag: notification._id,
        });
      }
    });

    // Listen for new ticket notifications
    adminChannel.bind('new-ticket-notification', (notification: NotificationDto) => {
      addNotification(notification);
      
      if (Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico',
          tag: notification._id,
        });
      }
    });

    // Listen for system notifications
    adminChannel.bind('system-notification', (notification: NotificationDto) => {
      addNotification(notification);
      
      if (Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico',
          tag: notification._id,
        });
      }
    });

    // Request notification permission if not already granted
    if (Notification.permission === 'default') {
      Notification.requestPermission();
    }

    return () => {
      adminChannel.unbind_all();
      adminChannel.unsubscribe();
    };
  }, [user, addNotification]);

  return null;
};
