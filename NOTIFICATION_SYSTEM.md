# Notification System Documentation

## Overview

This document describes the complete notification system implemented for the admin dashboard. The system provides real-time notifications for admin users when users send messages in ticket conversations, create new tickets, or when system events occur.

## Features

- **Real-time Notifications**: Admin users receive instant notifications via Pusher when events occur
- **Notification Badge**: Unread notification count displayed on the notification bell icon
- **Notification Dropdown**: Click the notification bell to view all notifications
- **Mark as Read**: Individual notifications can be marked as read by clicking on them
- **Mark All as Read**: Bulk action to mark all notifications as read
- **Browser Notifications**: Native browser notifications for important events (requires permission)
- **Notification Types**: Support for different types (message, ticket, order, system)

## Components

### 1. Notification Types (`src/types/notification.ts`)
Defines TypeScript interfaces for notification data structures.

### 2. Notification API (`src/api/notifications.ts`)
- `getNotifications()` - Fetch notifications with pagination
- `getNotificationCount()` - Get unread notification count
- `markNotificationsAsRead()` - Mark specific notifications as read
- `markAllNotificationsAsRead()` - Mark all notifications as read

### 3. Notification Hooks (`src/hooks/useNotifications.ts`)
- `useNotifications()` - Fetch and manage notifications
- `useNotificationCount()` - Get unread count with auto-refresh
- `useMarkNotificationsAsRead()` - Mark notifications as read
- `useUpdateNotifications()` - Real-time notification updates

### 4. Notification Dropdown (`src/components/notifications/NotificationDropdown.tsx`)
Interactive dropdown component that displays notifications when the bell icon is clicked.

### 5. Notification Listener (`src/listener/NotificationListener.ts`)
Real-time listener that subscribes to Pusher channels for admin notifications.

### 6. Updated Topbar (`src/layouts/main-layout/topbar/index.tsx`)
Enhanced notification bell with badge and click handler.

## Backend Requirements

The notification system expects the following backend endpoints:

### API Endpoints
- `GET /notifications` - Fetch notifications (with pagination)
- `GET /notifications/count` - Get unread notification count
- `PATCH /notifications/mark-read` - Mark specific notifications as read
- `PATCH /notifications/mark-all-read` - Mark all notifications as read

### Pusher Channels
- `admin-notifications` - Channel for admin-specific notifications

### Pusher Events
- `new-message-notification` - When a user sends a message
- `new-ticket-notification` - When a new ticket is created
- `system-notification` - For system-wide notifications

## Usage

### For Admin Users
1. **Viewing Notifications**: Click the notification bell icon in the header
2. **Reading Notifications**: Click on individual notifications to mark them as read
3. **Bulk Actions**: Use "Mark all read" button to clear all notifications
4. **Browser Notifications**: Grant permission when prompted for native notifications

### For Developers

#### Adding New Notification Types
1. Update the `NotificationDto` type in `src/types/notification.ts`
2. Add the new type to the notification icon/color mapping in `NotificationDropdown.tsx`
3. Update the backend to send notifications with the new type

#### Testing
Use the `NotificationTest` component on the dashboard to simulate different notification types.

## Real-time Flow

1. **User Action**: User sends a message in a ticket conversation
2. **Backend Processing**: Backend processes the message and creates a notification
3. **Pusher Event**: Backend sends notification via Pusher to `admin-notifications` channel
4. **Frontend Reception**: `NotificationListener` receives the event
5. **State Update**: Notification is added to the local state
6. **UI Update**: Badge count updates and notification appears in dropdown
7. **Browser Notification**: Native notification shown (if permission granted)

## Configuration

### Environment Variables
Ensure the following environment variables are set:
- `VITE_BASE_URI` - Backend API base URL

### Pusher Configuration
The Pusher client is configured in `src/listener/pusher.ts` with:
- App Key: `09b1adba58d8659c01d4`
- Cluster: `ap2`
- Auth endpoint: `${VITE_BASE_URI}pusher/auth`

## Security

- All API calls require authentication (`useAuth: true`)
- Only admin users receive notifications (role check in `NotificationListener`)
- Pusher authentication ensures secure channel access

## Performance Considerations

- Notifications are cached for 30 seconds to reduce API calls
- Notification count refreshes every 30 seconds automatically
- Pagination limits notifications to 20 per page by default
- Real-time updates use efficient state management to prevent unnecessary re-renders

## Troubleshooting

### Common Issues

1. **Notifications not appearing**: Check if user has admin role
2. **Real-time not working**: Verify Pusher configuration and network connectivity
3. **API errors**: Check backend endpoints and authentication
4. **Browser notifications not showing**: Ensure user granted notification permission

### Debug Steps

1. Check browser console for errors
2. Verify Pusher connection in Network tab
3. Test API endpoints manually
4. Use the NotificationTest component to simulate notifications
