export interface NotificationDto {
  _id: string;
  title: string;
  message: string;
  type: 'message' | 'ticket' | 'system' | 'order';
  read: boolean;
  userId: string;
  relatedId?: string; // conversation ID, ticket ID, etc.
  metadata?: {
    conversationId?: string;
    ticketId?: string;
    senderId?: string;
    senderName?: string;
    senderAvatar?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface NotificationResponse {
  notifications: NotificationDto[];
  unreadCount: number;
  totalCount: number;
}

export interface NotificationCountResponse {
  unreadCount: number;
}

export interface MarkAsReadRequest {
  notificationIds: string[];
}

export interface MarkAsReadResponse {
  message: string;
  updatedCount: number;
}
