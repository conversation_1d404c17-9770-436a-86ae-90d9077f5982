import React from 'react';
import { <PERSON><PERSON>, <PERSON>ack, Typography, Paper, Box } from '@mui/material';
import { useUpdateNotifications } from 'hooks/useNotifications';
import { NotificationDto } from 'types/notification';

const NotificationTest: React.FC = () => {
  const { addNotification } = useUpdateNotifications();

  const createTestNotification = (type: NotificationDto['type']) => {
    const testNotifications: Record<NotificationDto['type'], Partial<NotificationDto>> = {
      message: {
        title: 'New Message',
        message: '<PERSON> sent you a message in ticket #1234',
        type: 'message',
        metadata: {
          conversationId: 'test-conv-1',
          senderId: 'user-123',
          senderName: '<PERSON>',
          senderAvatar: 'https://images.unsplash.com/photo-1633332755192-727a05c4013d?w=100',
        },
      },
      ticket: {
        title: 'New Support Ticket',
        message: 'A new support ticket has been created by <PERSON>',
        type: 'ticket',
        metadata: {
          ticketId: 'ticket-456',
          senderId: 'user-456',
          senderName: '<PERSON>',
        },
      },
      order: {
        title: 'New Order',
        message: 'Order #ORD-789 has been placed',
        type: 'order',
        metadata: {
          senderId: 'user-789',
          senderName: 'Customer Name',
        },
      },
      system: {
        title: 'System Update',
        message: 'System maintenance scheduled for tonight',
        type: 'system',
      },
    };

    const baseNotification: NotificationDto = {
      _id: `test-${Date.now()}-${Math.random()}`,
      title: '',
      message: '',
      type: 'message',
      read: false,
      userId: 'admin-user',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...testNotifications[type],
    };

    addNotification(baseNotification);
  };

  return (
    <Paper sx={{ p: 3, m: 2 }}>
      <Typography variant="h6" gutterBottom>
        Notification System Test
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Use these buttons to test the notification system. Click the notification bell in the header to see the notifications.
      </Typography>
      
      <Stack spacing={2} direction="row" flexWrap="wrap">
        <Button
          variant="contained"
          color="primary"
          onClick={() => createTestNotification('message')}
        >
          Test Message Notification
        </Button>
        
        <Button
          variant="contained"
          color="warning"
          onClick={() => createTestNotification('ticket')}
        >
          Test Ticket Notification
        </Button>
        
        <Button
          variant="contained"
          color="success"
          onClick={() => createTestNotification('order')}
        >
          Test Order Notification
        </Button>
        
        <Button
          variant="contained"
          color="info"
          onClick={() => createTestNotification('system')}
        >
          Test System Notification
        </Button>
      </Stack>

      <Box sx={{ mt: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          <strong>Note:</strong> In a real application, notifications would be received via Pusher 
          when users send messages or create tickets. This test component simulates those notifications 
          for testing purposes.
        </Typography>
      </Box>
    </Paper>
  );
};

export default NotificationTest;
