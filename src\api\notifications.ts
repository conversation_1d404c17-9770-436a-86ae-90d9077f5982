import { getRequest, patchRequest } from 'vbrae-utils';
import { 
  NotificationResponse, 
  NotificationCountResponse, 
  MarkAsReadRequest, 
  MarkAsReadResponse 
} from 'types/notification';

interface GetNotificationsParams {
  page?: number;
  limit?: number;
  unreadOnly?: boolean;
}

export async function getNotifications({
  page = 1,
  limit = 20,
  unreadOnly = false
}: GetNotificationsParams = {}): Promise<NotificationResponse> {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(unreadOnly && { unreadOnly: 'true' })
  });

  return await getRequest({
    url: `notifications?${params.toString()}`,
    useAuth: true,
  });
}

export async function getNotificationCount(): Promise<NotificationCountResponse> {
  return await getRequest({
    url: 'notifications/count',
    useAuth: true,
  });
}

export async function markNotificationsAsRead(
  notificationIds: string[]
): Promise<MarkAsReadResponse> {
  const data: MarkAsReadRequest = { notificationIds };
  
  return await patchRequest({
    url: 'notifications/mark-read',
    data,
    useAuth: true,
  });
}

export async function markAllNotificationsAsRead(): Promise<MarkAsReadResponse> {
  return await patchRequest({
    url: 'notifications/mark-all-read',
    data: {},
    useAuth: true,
  });
}
