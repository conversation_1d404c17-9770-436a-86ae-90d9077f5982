import { useQuery, useMutation, useQueryClient } from 'react-query';
import { showError, useQueryFix } from 'vbrae-utils';
import { 
  getNotifications, 
  getNotificationCount, 
  markNotificationsAsRead, 
  markAllNotificationsAsRead 
} from 'api/notifications';
import { NotificationDto } from 'types/notification';

interface UseNotificationsParams {
  page?: number;
  limit?: number;
  unreadOnly?: boolean;
  enabled?: boolean;
}

export const useNotifications = ({
  page = 1,
  limit = 20,
  unreadOnly = false,
  enabled = true
}: UseNotificationsParams = {}) => {
  const { data, loading: notificationsLoading, refetch } = useQueryFix({
    query: useQuery({
      queryKey: ['notifications', { page, limit, unreadOnly }],
      queryFn: () => getNotifications({ page, limit, unreadOnly }),
      onError: showError,
      refetchOnWindowFocus: false,
      enabled,
      staleTime: 30000, // 30 seconds
    }),
    transform: (data) => data,
  });

  return {
    notifications: data?.notifications || [],
    unreadCount: data?.unreadCount || 0,
    totalCount: data?.totalCount || 0,
    notificationsLoading,
    refetchNotifications: refetch,
  };
};

export const useNotificationCount = () => {
  const { data: countData, loading: countLoading, refetch } = useQueryFix({
    query: useQuery({
      queryKey: ['notification-count'],
      queryFn: getNotificationCount,
      onError: showError,
      refetchOnWindowFocus: false,
      refetchInterval: 30000, // Refetch every 30 seconds
      staleTime: 15000, // 15 seconds
    }),
    transform: (data) => data,
  });

  return {
    unreadCount: countData?.unreadCount || 0,
    countLoading,
    refetchCount: refetch,
  };
};

export const useMarkNotificationsAsRead = () => {
  const queryClient = useQueryClient();

  const { mutateAsync: markAsRead, isLoading: markingAsRead } = useMutation(
    markNotificationsAsRead,
    {
      onError: showError,
      onSuccess: () => {
        // Invalidate and refetch notification queries
        queryClient.invalidateQueries(['notifications']);
        queryClient.invalidateQueries(['notification-count']);
      },
    }
  );

  const { mutateAsync: markAllAsRead, isLoading: markingAllAsRead } = useMutation(
    markAllNotificationsAsRead,
    {
      onError: showError,
      onSuccess: () => {
        // Invalidate and refetch notification queries
        queryClient.invalidateQueries(['notifications']);
        queryClient.invalidateQueries(['notification-count']);
      },
    }
  );

  return {
    markAsRead,
    markAllAsRead,
    markingAsRead,
    markingAllAsRead,
  };
};

// Hook to update notifications in real-time
export const useUpdateNotifications = () => {
  const queryClient = useQueryClient();

  const addNotification = (notification: NotificationDto) => {
    // Update notifications list
    queryClient.setQueryData(['notifications'], (oldData: any) => {
      if (!oldData) return { notifications: [notification], unreadCount: 1, totalCount: 1 };
      
      return {
        ...oldData,
        notifications: [notification, ...oldData.notifications],
        unreadCount: oldData.unreadCount + 1,
        totalCount: oldData.totalCount + 1,
      };
    });

    // Update notification count
    queryClient.setQueryData(['notification-count'], (oldData: any) => ({
      unreadCount: (oldData?.unreadCount || 0) + 1,
    }));
  };

  const updateNotificationAsRead = (notificationId: string) => {
    // Update notifications list
    queryClient.setQueryData(['notifications'], (oldData: any) => {
      if (!oldData) return oldData;
      
      const updatedNotifications = oldData.notifications.map((notification: NotificationDto) =>
        notification._id === notificationId
          ? { ...notification, read: true }
          : notification
      );

      return {
        ...oldData,
        notifications: updatedNotifications,
        unreadCount: Math.max(0, oldData.unreadCount - 1),
      };
    });

    // Update notification count
    queryClient.setQueryData(['notification-count'], (oldData: any) => ({
      unreadCount: Math.max(0, (oldData?.unreadCount || 0) - 1),
    }));
  };

  return {
    addNotification,
    updateNotificationAsRead,
  };
};
