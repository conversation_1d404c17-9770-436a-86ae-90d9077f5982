import React, { useState } from 'react';
import {
  <PERSON>u,
  <PERSON>uI<PERSON>,
  <PERSON><PERSON><PERSON>,
  Box,
  Avatar,
  Stack,
  Divider,
  Button,
  CircularProgress,
  IconButton,
  Chip,
} from '@mui/material';
import { format } from 'date-fns';
import IconifyIcon from 'components/base/IconifyIcon';
import { useNotifications, useMarkNotificationsAsRead } from 'hooks/useNotifications';
import { NotificationDto } from 'types/notification';

interface NotificationDropdownProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  anchorEl,
  open,
  onClose,
}) => {
  const [page] = useState(1);
  const { notifications, notificationsLoading, unreadCount } = useNotifications({
    page,
    limit: 10,
    enabled: open,
  });
  const { markAsRead, markAllAsRead, markingAsRead, markingAllAsRead } = useMarkNotificationsAsRead();

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead([notificationId]);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  const getNotificationIcon = (type: NotificationDto['type']) => {
    switch (type) {
      case 'message':
        return 'mdi:message-text';
      case 'ticket':
        return 'mdi:ticket';
      case 'order':
        return 'mdi:shopping';
      case 'system':
        return 'mdi:cog';
      default:
        return 'mdi:bell';
    }
  };

  const getNotificationColor = (type: NotificationDto['type']) => {
    switch (type) {
      case 'message':
        return 'primary.main';
      case 'ticket':
        return 'warning.main';
      case 'order':
        return 'success.main';
      case 'system':
        return 'info.main';
      default:
        return 'text.secondary';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return format(date, 'MMM dd, HH:mm');
  };

  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      PaperProps={{
        elevation: 0,
        sx: {
          mt: 1.5,
          width: 380,
          maxHeight: 500,
          overflow: 'hidden',
          border: '1px solid',
          borderColor: 'divider',
          '& .MuiMenuItem-root': {
            px: 0,
          },
        },
      }}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
    >
      {/* Header */}
      <Box sx={{ px: 2, py: 1.5, borderBottom: '1px solid', borderColor: 'divider' }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h6" fontWeight={600}>
            Notifications
            {unreadCount > 0 && (
              <Chip
                label={unreadCount}
                size="small"
                color="primary"
                sx={{ ml: 1, height: 20, fontSize: '0.75rem' }}
              />
            )}
          </Typography>
          {unreadCount > 0 && (
            <Button
              size="small"
              onClick={handleMarkAllAsRead}
              disabled={markingAllAsRead}
              sx={{ fontSize: '0.75rem' }}
            >
              {markingAllAsRead ? (
                <CircularProgress size={16} />
              ) : (
                'Mark all read'
              )}
            </Button>
          )}
        </Stack>
      </Box>

      {/* Notifications List */}
      <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
        {notificationsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
            <CircularProgress size={24} />
          </Box>
        ) : notifications.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 3, px: 2 }}>
            <IconifyIcon
              icon="mdi:bell-off"
              sx={{ fontSize: 48, color: 'text.disabled', mb: 1 }}
            />
            <Typography variant="body2" color="text.secondary">
              No notifications yet
            </Typography>
          </Box>
        ) : (
          notifications.map((notification, index) => (
            <React.Fragment key={notification._id}>
              <MenuItem
                onClick={() => !notification.read && handleMarkAsRead(notification._id)}
                sx={{
                  px: 2,
                  py: 1.5,
                  bgcolor: notification.read ? 'transparent' : 'action.hover',
                  '&:hover': {
                    bgcolor: notification.read ? 'action.hover' : 'action.selected',
                  },
                }}
              >
                <Stack direction="row" spacing={2} width="100%">
                  <Box sx={{ position: 'relative' }}>
                    {notification.metadata?.senderAvatar ? (
                      <Avatar
                        src={notification.metadata.senderAvatar}
                        sx={{ width: 40, height: 40 }}
                      />
                    ) : (
                      <Avatar sx={{ width: 40, height: 40, bgcolor: getNotificationColor(notification.type) }}>
                        <IconifyIcon icon={getNotificationIcon(notification.type)} />
                      </Avatar>
                    )}
                    {!notification.read && (
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          right: 0,
                          width: 12,
                          height: 12,
                          bgcolor: 'primary.main',
                          borderRadius: '50%',
                          border: '2px solid',
                          borderColor: 'background.paper',
                        }}
                      />
                    )}
                  </Box>
                  
                  <Stack spacing={0.5} flex={1} minWidth={0}>
                    <Typography
                      variant="body2"
                      fontWeight={notification.read ? 400 : 600}
                      sx={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                      }}
                    >
                      {notification.title}
                    </Typography>
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                      }}
                    >
                      {notification.message}
                    </Typography>
                    <Typography variant="caption" color="text.disabled">
                      {formatTimeAgo(notification.createdAt)}
                    </Typography>
                  </Stack>

                  {markingAsRead && (
                    <CircularProgress size={16} />
                  )}
                </Stack>
              </MenuItem>
              {index < notifications.length - 1 && <Divider />}
            </React.Fragment>
          ))
        )}
      </Box>

      {/* Footer */}
      {notifications.length > 0 && (
        <>
          <Divider />
          <Box sx={{ px: 2, py: 1 }}>
            <Button
              fullWidth
              size="small"
              onClick={onClose}
              sx={{ fontSize: '0.75rem' }}
            >
              View All Notifications
            </Button>
          </Box>
        </>
      )}
    </Menu>
  );
};

export default NotificationDropdown;
